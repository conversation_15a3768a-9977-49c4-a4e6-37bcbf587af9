import { AIAgentRequest, AIAgentRequestSchema } from './schemas';

export async function sendToAIAgent(payload: AIAgentRequest): Promise<Response> {
  // Validasi payload
  const validatedPayload = AIAgentRequestSchema.parse(payload);

  const url = process.env.AI_AGENT_URL || 'https://n8n.finityhub.ai/webhook-test/bht';

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(validatedPayload),
  });

  if (!response.ok) {
    throw new Error(`AI Agent error: ${response.status}`);
  }

  return response;
}
